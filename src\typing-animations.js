// Beautiful Typing and Deletion Animations for Notes App
class TypingAnimations {
    constructor(editorElement) {
        this.editor = editorElement;
        this.isAnimating = false;
        this.lastValue = '';
        this.typingTimeout = null;
        this.deleteTimeout = null;
        this.cursorPosition = 0;
        
        this.init();
    }
    
    init() {
        // Store initial value
        this.lastValue = this.editor.value;
        
        // Add event listeners
        this.editor.addEventListener('input', (e) => this.handleInput(e));
        this.editor.addEventListener('keydown', (e) => this.handleKeyDown(e));
        this.editor.addEventListener('focus', () => this.handleFocus());
        this.editor.addEventListener('blur', () => this.handleBlur());
        
        // Add typing mode class
        this.editor.classList.add('typing-effect');
    }
    
    handleInput(e) {
        const currentValue = this.editor.value;
        const currentPos = this.editor.selectionStart;
        
        // Determine if typing or deleting
        if (currentValue.length > this.lastValue.length) {
            this.handleTyping(currentPos, currentValue, this.lastValue);
        } else if (currentValue.length < this.lastValue.length) {
            this.handleDeleting(currentPos, currentValue, this.lastValue);
        }
        
        this.lastValue = currentValue;
        this.updateTypingMode();
    }
    
    handleKeyDown(e) {
        // Handle special keys
        if (e.key === 'Enter') {
            this.handleNewLine();
        } else if (e.key === ' ') {
            this.handleSpaceBar();
        }
    }
    
    handleTyping(position, newValue, oldValue) {
        // Add typing mode to editor
        this.editor.classList.add('typing-mode');
        
        // Create ripple effect for new characters
        this.createTypingRipple(position);
        
        // Add word completion effect
        this.handleWordCompletion(newValue, position);
        
        // Reset typing timeout
        clearTimeout(this.typingTimeout);
        this.typingTimeout = setTimeout(() => {
            this.editor.classList.remove('typing-mode');
        }, 1000);
    }
    
    handleDeleting(position, newValue, oldValue) {
        // Add deletion effect
        this.createDeletionEffect(position);
        
        // Add subtle shake effect
        this.editor.style.transform = 'translateX(1px)';
        setTimeout(() => {
            this.editor.style.transform = 'translateX(-1px)';
            setTimeout(() => {
                this.editor.style.transform = 'translateX(0)';
            }, 50);
        }, 50);
    }
    
    handleNewLine() {
        // Create line break animation
        setTimeout(() => {
            this.createLineRipple();
        }, 50);
    }
    
    handleSpaceBar() {
        // Create word completion effect
        setTimeout(() => {
            this.createWordHighlight();
        }, 50);
    }
    
    handleFocus() {
        this.editor.classList.add('active');
        this.createFocusAnimation();
    }
    
    handleBlur() {
        this.editor.classList.remove('active');
        this.editor.classList.remove('typing-mode');
    }
    
    createTypingRipple(position) {
        // Create a subtle glow effect at cursor position
        const rect = this.editor.getBoundingClientRect();
        const ripple = document.createElement('div');
        ripple.className = 'typing-ripple';
        ripple.style.cssText = `
            position: absolute;
            width: 4px;
            height: 20px;
            background: linear-gradient(180deg, 
                rgba(0, 180, 216, 0.6) 0%, 
                rgba(0, 180, 216, 0.3) 50%, 
                transparent 100%);
            border-radius: 2px;
            pointer-events: none;
            z-index: 1000;
            animation: typingPulse 0.3s ease-out;
        `;
        
        // Position the ripple (approximate)
        const lineHeight = parseInt(getComputedStyle(this.editor).lineHeight) || 20;
        const lines = this.editor.value.substring(0, position).split('\n').length - 1;
        
        ripple.style.left = rect.left + 25 + 'px';
        ripple.style.top = rect.top + 20 + (lines * lineHeight) + 'px';
        
        document.body.appendChild(ripple);
        
        // Remove after animation
        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
            }
        }, 300);
    }
    
    createDeletionEffect(position) {
        // Create deletion particle effect
        const rect = this.editor.getBoundingClientRect();
        
        for (let i = 0; i < 3; i++) {
            const particle = document.createElement('div');
            particle.className = 'deletion-particle';
            particle.style.cssText = `
                position: absolute;
                width: 3px;
                height: 3px;
                background: rgba(255, 107, 107, 0.7);
                border-radius: 50%;
                pointer-events: none;
                z-index: 1000;
                animation: deleteParticle 0.4s ease-out forwards;
            `;
            
            // Random positioning around cursor
            const lineHeight = parseInt(getComputedStyle(this.editor).lineHeight) || 20;
            const lines = this.editor.value.substring(0, position).split('\n').length - 1;
            
            particle.style.left = rect.left + 25 + (Math.random() * 10 - 5) + 'px';
            particle.style.top = rect.top + 20 + (lines * lineHeight) + (Math.random() * 10 - 5) + 'px';
            particle.style.animationDelay = (i * 0.1) + 's';
            
            document.body.appendChild(particle);
            
            // Remove after animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 500);
        }
    }
    
    createLineRipple() {
        // Add line ripple effect
        const lines = this.editor.value.split('\n');
        const currentLine = lines.length - 1;
        
        // Create temporary highlight for new line
        this.editor.classList.add('line-ripple');
        setTimeout(() => {
            this.editor.classList.remove('line-ripple');
        }, 800);
    }
    
    createWordHighlight() {
        // Highlight the last completed word
        const cursorPos = this.editor.selectionStart;
        const textBefore = this.editor.value.substring(0, cursorPos);
        const words = textBefore.split(/\s+/);
        const lastWord = words[words.length - 2]; // -2 because last element is empty after space
        
        if (lastWord && lastWord.length > 2) {
            // Create a brief highlight effect
            this.editor.style.background = 'rgba(0, 180, 216, 0.05)';
            setTimeout(() => {
                this.editor.style.background = 'transparent';
            }, 200);
        }
    }
    
    createFocusAnimation() {
        // Animate focus with a subtle scale and glow
        this.editor.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        this.editor.style.transform = 'scale(1.002)';
        this.editor.style.boxShadow = '0 0 20px rgba(0, 180, 216, 0.1)';
        
        setTimeout(() => {
            this.editor.style.transform = 'scale(1)';
        }, 300);
    }
    
    updateTypingMode() {
        // Update cursor blinking based on activity
        clearTimeout(this.typingTimeout);
        this.editor.classList.add('active');
        
        this.typingTimeout = setTimeout(() => {
            this.editor.classList.remove('active');
        }, 2000);
    }
    
    // Method to create typewriter effect for placeholder or demo text
    typewriterEffect(text, speed = 50) {
        return new Promise((resolve) => {
            let i = 0;
            this.editor.value = '';
            
            const typeChar = () => {
                if (i < text.length) {
                    this.editor.value += text.charAt(i);
                    this.createTypingRipple(i);
                    i++;
                    setTimeout(typeChar, speed + Math.random() * 30);
                } else {
                    resolve();
                }
            };
            
            typeChar();
        });
    }
}

// Add CSS animations dynamically
const style = document.createElement('style');
style.textContent = `
    @keyframes typingPulse {
        0% { opacity: 0; transform: scale(0.5); }
        50% { opacity: 1; transform: scale(1.2); }
        100% { opacity: 0; transform: scale(1); }
    }
    
    @keyframes deleteParticle {
        0% { 
            opacity: 1; 
            transform: scale(1) translateY(0); 
        }
        100% { 
            opacity: 0; 
            transform: scale(0.3) translateY(-15px) translateX(${Math.random() * 20 - 10}px); 
        }
    }
`;
document.head.appendChild(style);

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    const editor = document.getElementById('editor');
    if (editor) {
        window.typingAnimations = new TypingAnimations(editor);
    }
});
